.web_dev-page-container {
  .web_dev-page-content {
    .web_dev-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 100vh;
      width: 100%;

      .ellipse-container {
        width: 100%;
        height: 100vh;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .hero-content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 2;
        color: #fff;
        text-align: center;
        justify-content: center;

        .title {
          font-family: Inter !important;
          font-weight: 800;
          font-size: 96px;
          leading-trim: Cap height;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          color: #97a3aa;
          // margin-top: 200px;
          // margin-bottom: 41px;
          width: 100%;
          display: flex;
          justify-content: center;
          text-align: center;
          // margin-right: 100px;

          @media (max-width: 768px) {
            font-size: 48px;
            margin-top: 100px;
          }
        }
      }
    }

    .content {
      background-color: #ffffff;

      .web_dev-section {
        padding-bottom: 100px;
        padding-top: 124px;
        background-color: #fff;
        color: #001219;

        .header-container {
          display: flex;
          flex-direction: column;
          margin-bottom: 78px;

          .image-container {
            width: 100%;
            margin-top: 98px;

            img {
              width: 100%;
              height: auto;
              object-fit: cover;
            }
          }
        }

        .title {
          font-family: Inter !important;
          font-weight: 800;
          font-size: 48px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: left;
          margin-bottom: 50px;
          padding-left: 95px;
          padding-right: 95px;
        }

        .description-container {
          padding-left: 95px;
          padding-right: 95px;
          display: flex;
          justify-content: space-between;
          gap: 296px;
        }
      }

      .tool-design-section {
        padding-top: 90px;
        padding-left: 90px;
        padding-right: 90px;
        padding-bottom: 132px;
        // justify-content: space-between;

        .left-section {
          // max-width: 40%;
          display: flex;
          justify-content: space-between;

          .title {
            font-family: Inter !important;
            font-weight: 800;
            font-style: Extra Bold;
            font-size: 48px;
            leading-trim: CAP_HEIGHT;
            line-height: 100%;
            letter-spacing: 0%;
          }

          .left-content {
            max-width: 580px;

            .left-sub-title {
              font-family: Montserrat !important;
              font-weight: 500;
              font-style: Medium;
              font-size: 16px;
              leading-trim: CAP_HEIGHT;
              line-height: 100%;
              letter-spacing: 0%;
              color: #021f2e;
            }
          }
        }

        .right-section {
          max-width: 60%;
          margin-top: 34px;
          margin-left: 129px;

          .description-box {
            padding: 62px 124px 59px 52px;
            background: rgba(215, 125, 70, 0.19);
            border-radius: 8px;
            margin-bottom: 45px;

            p {
              font-family: Montserrat !important;
              font-weight: 500;
              font-size: 16px;
              leading-trim: Cap height;
              line-height: 100%;
              letter-spacing: 0%;
              color: #021f2e;
            }
          }

          .cards {
            margin-top: 60px;

            .card {
              max-width: 513px;
              padding-left: 54px;
              margin-bottom: 65px;
              display: flex;
              gap: 45px;

              img {
                margin-bottom: 20px;
              }

              .description {
                font-family: Montserrat !important;
                font-weight: 500;
                font-size: 16px;
                leading-trim: Cap height;
                line-height: 100%;
                letter-spacing: 0%;
                color: #021f2e;
              }
            }
          }
        }
      }

      .swiper-section {
        // .title {
        //   display: none;
        // }
      }

      .our-expand-section {
        display: flex;
        justify-content: center;
        margin-top: 100px;
        padding-right: 95px;
        padding-left: 95px;
        padding-bottom: 156px;

        .web-dev-expand-image {
          width: 100%;
          height: auto;
          object-fit: cover;
        }
      }
    }
  }
}
