"use client";

import { useState } from "react";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import "./SystemTesting.scss";
import {
  systemtestingbanner,
  SystemTestingWhychooseus,
  ToolandPlatform,
} from "@/public";
import WhatWeOffer from "@/components/Whatweoffer/WhatWeOffer";
import CommunicationTestingDialog from "@/components/CommunicationTestingDialog/CommunicationTestingDialog";
import SystemReliabilityDialog from "@/components/SystemReliabilityDialog/SystemReliabilityDialog";
import SoftwareTestingDialog from "@/components/SoftwareTestingDialog/SoftwareTestingDialog";

const SystemTestingPage = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  type Service = { title: string };
  const [selectedService, setSelectedService] = useState<Service | null>(null);

  const services: Service[] = [
    { title: "System Reliability Testing" },
    { title: "Communication testing" },
    { title: "Simulation Testing" },
    { title: "Software Testing" },
    { title: "Environmental & Mechanical Endurance" },
    { title: "Regulatory & Certification" },
  ];

  // Handler to open dialog when a service is clicked
  const handleServiceClick = (service: any) => {
    setSelectedService(service);
    setDialogOpen(true);
  };

  // Handler to close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedService(null);
  };

  // Dynamic content for all services
  const getDialogContent = (serviceTitle: string) => {
    if (serviceTitle === "Communication testing") {
      return {
        title: "Communication Testing",
        leftDescriptions: [
          "Any IoT systems typically use low-power communication protocols like MQTT, Zigbee, LoRa, Bluetooth, NB-IoT. Mostly these device operated over battery power and expected to perform up to last mile. Our experts understand the criticality involved in performing such communication test to ensure the function effectiveness in real-world conditions.",
        ],
        sections: [
          {
            title: "Connectivity Testing",
            description:
              "Ensuring the device can establish and maintain healthy connection in all weather conditions. Verifying that system design and performance for network registration, signal strength, and loss recovery.",
          },
          {
            title: "Interoperability Testing",
            description:
              "Ensures devices can communicate with other systems, platforms, or devices using different protocols or vendors. Cross-Device Compatibility testing across different hardware and firmware configurations.",
          },
          {
            title: "Latency and Throughput Testing",
            description:
              "Aadvik Team ensures reliable communication with consistent latency, maintaining predefined time intervals and data rates for data exchange between systems. This enables accurate evaluation of the system under test in terms of responsiveness and capacity.",
            hasButton: true,
            buttonText: "SOLUTION",
          },
          {
            title: "Security Testing",
            description:
              "Executing comprehensive safety of the data security by comprehensively enforcing it through robust encryption, authentication, and protective measures to safeguard against any external kind of cyber threats.",
          },
          {
            title: "Protocol Compliance Testing",
            description:
              "We offer protocol compliance testing services to ensure your devices and systems strictly adhere to industry communication standards. This testing is essential for achieving interoperability, certification, and reliable performance across multi-vendor environments.",
          },
          {
            title: "Power Consumption Test",
            description:
              "Assesses of power consumption need key engineering skillset to achieve the reliable test results. We assess communication frequency and mode affect battery life which is a crucial part for any low-power IoT devices.",
          },
        ],
      };
    } else if (serviceTitle === "Simulation Testing") {
      return {
        title: "Simulation Testing",
        leftDescriptions: [
          "Aadvik testing team builds a virtual simulation environment tailored to your use case, enabling the replication of real-world interactions. Our business-centric approach allows us to validate core system functionality and performance well in advance before actual field deployment.",
        ],
        sections: [
          {
            title: "Unit Testing",
            description:
              "Our engineers involves in testing each software modules functionally. The hardware blocks in isolation to ensure their functionality.",
          },
          {
            title: "System Testing",
            description:
              "Evaluates the overall operation, performance, and behavior of the entire embedded system, including its hardware and software components.",
          },
          {
            title: "Functional Simulation",
            description:
              "We perform focused simulation testing on verifying the functionality of the embedded software and runs test simulation programs on a virtual model of the hardware.",
          },
          {
            title: "Integration Testing",
            description:
              "Our focused approach to verify interrelation between different software modules and subsystems and ensure smooth integration and interoperability.",
          },
          {
            title: "Hardware Testing",
            description:
              "Aadvik TekLabs hardware team work with reliability and component such as sensors, actuators, and communication interfaces.",
          },
          {
            title: "Co-Simulation Test",
            description:
              "Focusing on firmware-hardware integration, timing, and logic behavior. Aadvik team runs hardware and software models together to validate interactions between firmware and physical devices.",
          },
        ],
      };
    } else if (serviceTitle === "Regulatory & Certification") {
      return {
        title: "Regulatory & Certification",
        leftDescriptions: [
          "Regulatory compliance are necessary to ensure that a product meets the technical requirements set by governing bodies whereas product certification is necessary to formally launch the product in market to confirms that a product or process has met specific regulations or standards. Aadvik TekLabs can assist you in getting your product verified to ensure it meets and complies with all relevant standards.",
        ],
        sections: [
          {
            title: "Developmental Testing",
            description:
              "Development testing is typically conducted in-house at each beta release stage to ensure that the system or product is accurate, reliable, and compliant with relevant standards.",
          },
          {
            title: "Integration Testing",
            description:
              "Our focused approach to verify interrelation between different software modules and subsystems and ensure smooth integration and interoperability.",
          },
          {
            title: "Certification Testing",
            description:
              "Our engineers provide assistance to conduct the certification testing on your behalf. This is typically done by independent accredited laboratories at their own facilities. The certification body performs all tests in accordance with the relevant standards applicable to the product under evaluation. Upon successful completion of testing, a certificate is issued to the manufacturer.",
          },
        ],
      };
    } else if (serviceTitle === "Software Testing") {
      return {
        title: "Software Testing",
        leftDescriptions: [
          "Aadvik TekLabs offers comprehensive software testing services to ensure your product is reliable, secure, and compliant with relevant standards. Our testing processes cover functionality, performance, compatibility, and security, helping you deliver a high-quality product to the market.",
          "Aadvik TekLabs provides actionable insights that drive smarter design decisions and enhance product longevity.",
        ],
        rightSideList: [
          "Understand and analyze software requirements & identify testable elements.",
          "Define and creation of test plan, testing strategy, scope, objectives, and resources.",
          "Develop detailed test cases and test scripts based on requirements.",
          "Creating test environment & setup for hardware, software, and network under test",
          "Test execution and defect reporting to the development team for resolution",
          "Regression testing and closure of tickets with final test results and report release.",
        ],
      };
    } else if (serviceTitle === "Environmental & Mechanical Endurance") {
      return {
        title: "Mechanical Endurance Testing",
        leftDescriptions: [
          "Mechanical endurance testing is conducted to evaluate how a product responds to prolonged or extreme mechanical stress under controlled laboratory conditions. Our design goal is to build the robust design which can perform well in extreme conditions.Purpose of test is ",
          "Aadvik TekLabs Team performs focused reliability testing to evaluate the durability and longevity of the components to ensure that a product can withstand extended use without mechanical failure",
        ],
        rightSideList: [
          "Assess wear and tear over time.",
          "Verify mechanical robustness under stress.",
          "Detect mechanical fatigue, failure points, or loose fittings.",
          "Ensure performance consistency throughout the product’s lifecycle.",
          "Validate compliance with mechanical life standards.",
        ],
      };
    }
    // Default content for other services
    return {
      title: "Default Testing",
      leftDescriptions: ["Default description for other services."],
      sections: [{ title: "Default", description: "Default content." }],
    };
  };

  const dialogContent = selectedService
    ? getDialogContent(selectedService.title)
    : null;

  return (
    <Box className="system-testing-page-container">
      <Box className="system-testing-page-content">
        <Box className="system-testing-page-header">
          <Box className="ellipse-container">
            <Image
              src={systemtestingbanner}
              alt="Ellipse"
              className="ellipse-image"
            />
            {/* <Box className="banner-gradient" /> */}
          </Box>
          <Box className="hero-content" />
        </Box>

        {/* Testing & Compliance Section */}
        <Box className="testing-compliance-section">
          <Box className="testing-left">
            <Typography className="testing-headline">
              <span>Committed</span> to delivers future ready product with <span>global</span> compliance,
              quality , reliability, <span>safety</span> and <span>performance</span>
            </Typography>
          </Box>
          <Box className="testing-right">
            <Typography paragraph className="testing-text">
              Our comprehensive testing services span functional validation,
              product reliability checks, and compliance with international
              standards such as ISO, IEC, UL, and CE. With a strong focus on
              safety and performance, our team conducts meticulous electrical
              and functional testing before any product leaves our facility.
            </Typography>
            <Typography paragraph className="testing-text">
              Backed by a dedicated quality assurance team, Aadvik TekLabs
              ensures that every embedded system is not just connected, but
              intelligent, intuitive, and ready for the future
            </Typography>
            <Typography paragraph className="testing-text">
              We guarantee that each system meets the highest benchmarks of
              reliability, safety, and global regulatory compliance.
            </Typography>
          </Box>
        </Box>

        {/* Specialization Section */}
        <Box className="specialization-section">
          <Typography component="h3" className="specialization-heading">
            Our specialization
          </Typography>
          <Typography className="specialization-body">
            Aadvik Engineers provide specialized support in reliability
            engineering and testing compliance with FCC, RoHS, and safety
            regulations, ensuring your product meets critical regulatory
            requirements and industry standards. We specialize in:
          </Typography>
        </Box>

        {/* What We Offer Section */}
        <Box className="what-we-offer-section">
          <WhatWeOffer
            services={services}
            onServiceClick={handleServiceClick}
          />
        </Box>
        <Box className="why-choose-us">
          <Image src={SystemTestingWhychooseus} alt="whychooseus" />
        </Box>

        <Box className="tools-platform-section">
          <Box className="tools-card">
            <Image
              src={ToolandPlatform}
              alt="Tools and Platforms for IoT Communication Testing"
              className="tools-image"
            />
          </Box>
          <Typography className="tools-caption">
            <em>
              Aadvik TekLabs provides comprehensive hardware and software
              testing services, ensuring products meet industry standards,
              deliver top performance, ensure user safety, and are
              market-readiness of product.
            </em>
          </Typography>
        </Box>

        {/* Render appropriate dialog based on the selected service */}
        {selectedService && (
          <>
            {selectedService.title === "System Reliability Testing" && (
              <SystemReliabilityDialog
                open={dialogOpen}
                onClose={handleCloseDialog}
              />
            )}
            {[
              "Communication testing",
              "Simulation Testing",
              "Regulatory & Certification",
            ].includes(selectedService.title) &&
              dialogContent && (
                <CommunicationTestingDialog
                  open={dialogOpen}
                  onClose={handleCloseDialog}
                  title={dialogContent.title}
                  leftDescriptions={dialogContent.leftDescriptions}
                  sections={dialogContent.sections}
                />
              )}
            {selectedService.title === "Software Testing" && dialogContent && (
              <SoftwareTestingDialog
                open={dialogOpen}
                onClose={handleCloseDialog}
                title={dialogContent.title}
                leftDescriptions={dialogContent.leftDescriptions}
                rightSideList={dialogContent.rightSideList || []}
              />
            )}
            {selectedService.title === "Environmental & Mechanical Endurance" &&
              dialogContent && (
                <SoftwareTestingDialog
                  open={dialogOpen}
                  onClose={handleCloseDialog}
                  title={dialogContent.title}
                  leftDescriptions={dialogContent.leftDescriptions}
                  rightSideList={dialogContent.rightSideList || []}
                />
              )}
          </>
        )}
      </Box>
    </Box>
  );
};

export default SystemTestingPage;
