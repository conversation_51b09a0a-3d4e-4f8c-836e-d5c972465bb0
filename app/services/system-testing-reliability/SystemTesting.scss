.system-testing-page-container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;

  .system-testing-page-content {
    width: 100vw;
    min-height: 100vh;
    position: relative;

    .system-testing-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
      position: relative;

      .ellipse-container {
        width: 100vw;
        height: 100vh;
        position: relative;
        z-index: 1;

        .banner-gradient {
          position: absolute;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          pointer-events: none;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.5) 100%
          );
          z-index: 2;
        }

        img {
          width: 100vw;
          height: 100vh;
          object-fit: cover;
          display: block;
        }
      }

      .hero-content {
        position: absolute;
        left: 0;
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        z-index: 2;
        color: #fff;
        text-align: center;
        padding-bottom: 148px;

        .title {
          font-family: Inter !important;
          font-weight: 800;
          max-width: 600px;
          font-size: 64px;
          letter-spacing: 0%;
          color: #fff;
          width: 100vw;
          display: flex;
          justify-content: center;
          align-items: flex-end;
          text-align: center;
          margin: 0;
          padding: 0 16px;

          @media (max-width: 768px) {
            font-size: 48px;
            margin-top: 100px;
          }
        }
      }
    }

    /* =========  TESTING & COMPLIANCE SECTION  ========= */
    .testing-compliance-section {
      padding: 107px 102px 150px 92px;
      display: flex;
      flex-wrap: wrap;
      gap: 200px;
      align-items: flex-start;
      background: #fff;

      .testing-left {
        flex: 1;
        max-width: 524px;
      }

      .testing-headline {
        font-family: Montserrat !important;
        font-weight: 400;
        font-style: Regular;
        font-size: 40px;
        letter-spacing: 0%;

        color: #d77d46;
        span {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
        }
      }

      .testing-right {
        flex: 1;
        max-width: 614px;
        display: flex;
        flex-direction: column;
        gap: 1.25rem;

        .testing-text {
          font-family: Montserrat;
          font-weight: 500;
          font-style: Medium;
          font-size: 20px;
          letter-spacing: 0%;
          color: #021f2e;
        }
      }
    }
    /* =========  OUR SPECIALIZATION SECTION  ========= */
    .specialization-section {
      padding: 0 131px 95px 92px;
      display: flex;
      flex-wrap: wrap;
      gap: 200px;
      align-items: flex-start;
      background: #fff;

      .specialization-heading {
        flex-grow: 1;
        // flex-shrink: 1;
        // flex-basis: 250px;
        max-width: 524px;
        font-family: Inter !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 40px;
        letter-spacing: 0%;

        color: #021f2e;
      }

      .specialization-body {
        flex: 1 1 380px;
        max-width: 615px;
        font-family: Montserrat !important;
        font-weight: 500;
        font-style: Medium;
        font-size: 20px;
        letter-spacing: 0%;
        color: #021f2e;
      }
    }
    .what-we-offer-section {
      background: #fff;
      //   padding: 80px 0px;
      padding: 0 95px 80px 95px;
    }
    .why-choose-us {
      padding: 77px 0 0 0;
      img {
        width: 100%;
        height: 656px !important;
        height: auto;
      }
    }
    /* ========= TOOLS & PLATFORM SECTION ========= */
    .tools-platform-section {
      padding: 93px 0 93px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 34px;
      background: #fff;

      .tools-card {
        max-width: 817px;
        width: 100%;
        display: flex;
        justify-content: center;

        .tools-image {
          max-width: 100%;
          height: auto;
          border-radius: 6px;
        }
      }

      .tools-caption {
        max-width: 740px;
        font-family: Poppins !important;
        font-weight: 400;
        font-style: Italic;
        font-size: 20px;
        letter-spacing: 0%;
        text-align: center;
        vertical-align: middle;
      }
    }
  }
}
