import { Box, Typography } from "@mui/material";
import "./CloudMobility.scss";
import Image from "next/image";
import {
  cloudmobilitybanner,
  CloudMobilityIcon,
  CloudMobilityImage,
  DevopsConsulting,
  ExpertiseIcon,
  ExpertiseImage,
} from "@/public";
import WhatWeOffer from "@/components/Whatweoffer/WhatWeOffer";

const CloudMobilityPage = () => {
  const services = [
    {
      title: "Cloud Architecture Design and development",
    },
    {
      title: "Web Application development & deployment",
    },
    {
      title: "Native & Cross-Platform App Development",
    },
    {
      title: "Intuitive & user centric UI/UX Design",
    },
    {
      title: "Backend Integration using Secure APIs",
    },
    {
      title: "App Launch on App Store/Google Play",
    },
    {
      title: "Support for maintenance and updates",
    },
    {
      title: "Functionality, performance, and security testing",
    },
  ];
  return (
    <Box className="cloud-mobility-page-container">
      <Box className="cloud-mobility-page-content">
        <Box className="cloud-mobility-page-header">
          <Box className="cloud-mobility-home-container">
            <Image
              src={cloudmobilitybanner}
              alt="Cloud Mobility Banner"
              className="ellipse-image"
            />
            
          </Box>
        </Box>
        <Box className="cloud-innovation-section">
          <Box className="cloud-innovation-content">
            <Box className="innovation-grid">
              <Box className="left-text">
                <Typography className="highlighted-heading">
                  <span className="highlight">Innovation</span> is at the core
                  <br />
                  of everything what we
                  <br />
                  deliver!
                </Typography>
                <Typography className="left-description">
                  We are constantly exploring the frontiers of cloud based
                  technology, with a focused on delivering the best and all time
                  available IT solutions poised to the requirements of
                  industries dynamics. We specialize in delivering next
                  generation web applications and software solutions tailored to
                  meet our clients’ evolving needs.
                </Typography>
              </Box>

              <Box className="right-text">
                <Box className="right-text-align">
                  <Box className="center-icon">
                    <Image
                      src={CloudMobilityIcon}
                      alt="Cloud Mobility Icon"
                      className="cloud-mobility-icon"
                      style={{ objectFit: "contain" }}
                    />
                  </Box>
                  <Typography className="right-description">
                    Aadvik Engineers turn ideas into reality, specializing in
                    end-to-end cloud platform development. At Aadvik TekLabs, we
                    build high-performing web and native apps with user-centric,
                    cross-platform designs to ensure marketplace success.
                  </Typography>
                  <br />
                  <Typography className="right-description-two">
                    Aadvik TekLabs crafts intuitive, user-friendly mobile apps
                    from concept to deployment. We deliver custom iOS, Android,
                    and cross-platform solutions with seamless UX, strong
                    performance, and scalable architecture aligned with your
                    business goals.
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>

        {/* what we offer */}
        <Box className="what-we-offer-section">
          <Typography variant="h2" className="what-we-offer__title">
            What we offer
          </Typography>
          <WhatWeOffer services={services} />
        </Box>

        {/* expertise with aadvikteklabs */}
        <Box className="expertise-section">
          <Box className="expertise-content">
            {/* Left Section */}
            <Box className="expertise-left">
              <Typography className="expertise-heading">
                Expertise with Aadvik TekLabs
              </Typography>
              <Typography className="expertise-description">
                By implementing a cloud-based IoT system and web applications
                with Aadvik TekLabs, you can leverage the power of the cloud to
                seamlessly integrate and manage your IoT infrastructure. We can
                help you to achieve -
              </Typography>

              <Box className="expertise-point">
                <Image src={ExpertiseIcon} alt="Icon" />
                <Typography className="expertise-point-text">
                  System Scalability - Easily grow as per your demand changes,
                  without investing much on expensive hardware
                </Typography>
              </Box>

              <Box className="expertise-point">
                <Image src={ExpertiseIcon} alt="Icon" />
                <Typography className="expertise-point-text">
                  Security - The cloud offers robust security features to
                  protect sensitive data collected by your devices.
                </Typography>
              </Box>

              <Box className="expertise-point">
                <Image src={ExpertiseIcon} alt="Icon" />
                <Typography className="expertise-point-text">
                  Cost-Effectiveness by getting alert & notifications, which
                  eliminate the need much investing on key resources
                </Typography>
              </Box>
            </Box>

            {/* Right Section */}
            <Box className="expertise-right">
              <Image
                src={ExpertiseImage}
                alt="Expertise Diagram"
                className="expertise-image"
              />
            </Box>
          </Box>
        </Box>

        {/* Devops Section */}
        <Box className="devops-section">
          <Box className="devops-container">
            <Typography className="devops-heading">
              Our DevOps team can assist <br />
              in evaluating your existing <br />
              infrastructure and workflows <br />
              to{" "}
              <span className="highlight">
                identify gaps and fill <br /> bottlenecks.
              </span>
            </Typography>
            <Box className="devops-description-block">
              <Typography className="devops-description">
                We design a tailored DevOps roadmap by selecting the right tools
                and defining CI/CD workflows. We manage cloud setup, automate
                infrastructure provisioning, and provide ongoing support to
                optimize performance and ensure long-term efficiency.
              </Typography>
            </Box>
          </Box>
        </Box>
        {/* Devops Consulting */}
        <Box className="devops-consulting">
          <Typography>DevOps Consulting</Typography>
          <Image src={DevopsConsulting} alt="Devops Consulting" />
        </Box>

        {/* Key Benifits Section */}
        <Box className="devops-benefits-section">
          <Box className="devops-benefits-content">
            <Box className="benefits-heading-block">
              <Typography className="benefits-heading">
                Key Benefits of working with Aadvik DevOps Team
              </Typography>
            </Box>

            <Box className="benefits-list">
              {[
                "Accelerated Time-to-Market by streamlining workflows and process automation.",
                "Enhanced Collaboration between development and operations teams for efficient workflows.",
                "Improved Quality of deliverables by automated testing and live monitoring.",
                "Greater Scalability and Flexibility for future technology migrations and business need.",
                "Better Security Compliance for audits and regulations.",
                "Continuous support and evolution.",
              ].map((item, index) => (
                <Box className="benefit-item" key={index}>
                  <Image src={ExpertiseIcon} alt="star icon" />
                  <Typography>{item}</Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default CloudMobilityPage;
