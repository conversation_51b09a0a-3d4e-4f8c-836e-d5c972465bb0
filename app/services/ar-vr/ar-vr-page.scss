.ar_vr-page-container {
  .ar_vr-page-content {
    .ar_vr-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 100vh;
      width: 100%;

      .ellipse-container {
        width: 100%;
        height: 100vh;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .hero-content {
        .title {
          font-family: Inter !important;
          font-weight: 800;
          font-size: 96px;
          letter-spacing: 0%;
          text-align: left;
          vertical-align: middle;
          color: #97a3aa;
          margin-top: 200px;
          margin-bottom: 41px;
          width: 100%;
          display: flex;
          justify-content: end;
          text-align: left;
          margin-right: 100px;

          @media (max-width: 768px) {
            font-size: 48px;
            margin-top: 100px;
          }
        }

        .header-image {
          color: transparent;
          position: absolute;
          left: inherit;
          bottom: 0;
          margin-left: 81px;
        }
      }
    }

    .content {
      background-color: #ffffff;

      .ar_vr-section {
        padding-bottom: 100px;
        padding-top: 124px;
        background-color: #fff;
        color: #001219;

        .header-container {
          display: flex;
          flex-direction: column;
          margin-bottom: 78px;

          .image-container {
            width: 100%;
            margin-top: 98px;

            img {
              width: 100%;
              height: auto;
              object-fit: cover;
            }
          }
        }

        .title {
          font-family: Inter !important;
          font-weight: 800;
          font-size: 48px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: left;
          margin-bottom: 50px;
          padding-left: 95px;
          padding-right: 95px;
        }

        .description-container {
          padding-left: 95px;
          padding-right: 95px;
          display: flex;
          justify-content: space-between;
          gap: 296px;
        }
      }

      .immersive-section {
        padding: 0px 60px 100px 94px;
        background-color: #ffffff;
        width: 100%;
        position: relative;

        @media (max-width: 900px) {
          padding: 60px 24px 80px;
        }

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 0;
          height: 20px;
          width: 100%;
        }
      }

      .immersive-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-wrap: wrap;
        gap: 40px;
      }

      .immersive-left {
        flex: 1 1 55%;
        min-width: 300px;

        .immersive-title {
          font-family: Inter !important;
          font-weight: 800;
          font-style: Extra Bold;
          font-size: 48px;
          letter-spacing: 0%;

          margin-bottom: 20px;
        }

        .immersive-paragraph {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          letter-spacing: 0%;
          max-width: 503px;
          margin-top: 70px;
        }
      }

      .immersive-right {
        margin-top: 140px;
        flex: 1 1 40%;
        min-width: 300px;
        display: flex;
        flex-direction: column;
        gap: 62px;

        .solution-item {
          display: flex;
          align-items: flex-start;
          gap: 16px;

          img {
            flex-shrink: 0;
            margin-top: 2px;
          }

          .solution-text {
            font-family: Montserrat !important;
            font-weight: 500;
            font-style: Medium;
            font-size: 16px;
            letter-spacing: 0%;
            max-width: 513px;
            color: #021f2e;
          }
        }
      }

      .simulation-section {
        padding-left: 90px;
        padding-right: 90px;
        padding-bottom: 205px;

        .title {
          font-family: Inter !important;
          font-weight: 800;
          font-size: 48px;
          letter-spacing: 0%;
          color: #021f2e;
          margin-bottom: 52px;
          max-width: 566px;
        }

        .simulation-content {
          max-width: 40%;

          .simulation-content-descrition {
            font-family: Montserrat !important;
            font-weight: 500;
            font-size: 16px;
            letter-spacing: 0%;
            color: #021f2e;
            margin-bottom: 35px;
          }

          .simulation-list {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: 24px;
          }

          .simulation-list-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 0;
          }

          .simulation-list-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            margin-top: 7px;
          }

          .simulation-list-text {
            font-family: Montserrat !important;
            font-weight: 500;
            font-style: Medium;
            font-size: 16px;
            letter-spacing: 0%;

            color: #021f2e;
          }
        }

        .simulation-image-container {
          padding: 28px;
          border: 1px dashed #d77d46;
          border-radius: 12px;

          .text {
            margin-top: 20px;
            font-family: Poppins;
            font-weight: 275;
            font-size: 24px;
            line-height: 100%;
            letter-spacing: 0%;
            text-align: center;
            text-decoration: underline;
            text-decoration-style: solid;
            text-decoration-thickness: 0%;
            text-decoration-skip-ink: auto;
          }

          &:hover {
            cursor: pointer;
            background-color: #d77d46;
            transition: background-color 0.3s ease;

            .text {
              color: #fff;
            }
          }
        }
      }
    }
  }
}
