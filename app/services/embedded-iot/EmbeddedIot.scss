.embedded-iot-page-container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;

  .embedded-iot-page-content {
    width: 100vw;
    min-height: 100vh;
    position: relative;

    .embedded-iot-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
      position: relative;

      .ellipse-container {
        width: 100vw;
        height: 100vh;
        position: relative;
        z-index: 1;

        .banner-gradient {
          position: absolute;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          pointer-events: none;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.5) 100%
          );
          z-index: 2;
        }

        img {
          width: 100vw;
          height: 100vh;
          object-fit: cover;
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }

      .hero-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;
        color: #fff;
        text-align: center;
        padding-bottom: 0;

        .title {
          font-family: Inter;
          font-weight: 800;
          font-style: Extra Bold;
          font-size: 64px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          width: 100%;
          height: 100%;
          margin: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    /* =========  NEXT SECTION STYLES  ========= */
    .embedded-solution-section {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      gap: 191px;
      padding: 98px 166px 68px 95px;
      background: #ffffff;
      width: 100%;

      @media (max-width: 960px) {
        flex-direction: column;
        text-align: center;
        gap: 2.5rem;
      }

      .solution-left,
      .solution-right {
        flex: 1;
      }

      .solution-left-container {
        max-width: 524px;
        .solution-title {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          color: #d77d46;
        }
      }
      .solution-text-first-contaqiner {
        max-width: 466px;
        .solution-text-first {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          letter-spacing: 0%;
          color: #021f2e;
        }
        .solution-text-second {
          margin-top: 30px;
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          letter-spacing: 0%;
          color: #021f2e;
        }
      }
    }
    /* =========  EMBEDDED PLATFORMS STYLES  ========= */
    .embedded-platforms-section {
      padding: 136px 220px 134px 214px;
      text-align: center;
      color: #ffffff;

      .platforms-title {
        font-family: Montserrat !important;
        font-weight: 700;
        font-style: Bold;
        font-size: 45px;
        line-height: 30px;
        letter-spacing: 0%;
        vertical-align: middle;
        margin-bottom: 69px;
      }

      .platform-subtitle-container {
        text-align: -webkit-center;
        margin-bottom: 68px;
        .platforms-subtitle {
          max-width: 815px;
          font-family: Poppins !important;
          font-weight: 400;
          font-style: Regular;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: center;
        }
      }

      .logos-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;

        .logo-strip {
          width: 100%;
          max-width: 950px;
          height: auto;
          object-fit: contain;
        }
      }
    }

    .offerings-section-main-container {
      padding: 92px 59px 92px 94px;
      background-color: #f8f9fa;
      .offerings-title {
        font-family: Inter !important;
        font-weight: 800;
        font-style: Extra Bold;
        font-size: 48px;
        line-height: 100%;
        color: #021f2e;
        letter-spacing: 0%;
        margin-bottom: 26px;
      }
      .offerings-divider {
        border: 1px solid #2499e280;
        margin-bottom: 51px;
      }
      .offerings-section {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;

        .offerings-left {
          flex: 1 1 700px;
          max-width: 900px;

          .offerings-list {
            display: flex;
            flex-direction: column;
            gap: 46px;

            .offering-item {
              display: flex;
              align-items: center;
              gap: 27px;
              cursor: pointer;
              // transition: all 0.2s ease-in-out;

              .offering-text {
                font-family: Montserrat !important;
                font-weight: 300;
                font-style: Italic;
                font-size: 20px;
                letter-spacing: 0%;

                //   transition: all 0.2s ease-in-out;
              }

              &:hover {
                .offering-text {
                  font-family: Montserrat !important;
                  font-weight: 700;
                  font-style: Bold;
                  font-size: 24px;
                  line-height: 100%;
                  letter-spacing: 0%;
                }
              }
            }
          }
        }

        .offerings-right {
          flex: 1 1 320px;
          display: flex;
          justify-content: center;

          .offering-main-image {
            width: 100%;
            max-width: 353px;
            height: auto;
            object-fit: contain;
          }
        }
      }
    }

    .iot-network-section {
      color: #ffffff;
      padding: 80px 88px 79px 88px;
      display: flex;
      flex-direction: column;
      gap: 78px;

      .iot-title {
        font-family: Inter !important;
        font-weight: 800;
        font-style: Extra Bold;
        font-size: 48px;
        line-height: 100%;
        letter-spacing: 0%;
      }

      .iot-main-content {
        display: flex;
        flex-wrap: wrap;
        align-items: stretch;
        justify-content: center;

        .iot-left-image {
          flex: 1 1 480px;
          max-height: 441px;
          max-width: 725px;

          .iot-img {
            width: 100%;
            height: auto;
            object-fit: contain;
          }
        }

        .iot-right-box {
          flex: 1 1 488px;
          max-height: 441px;
          background-color: #d77d46;
          //   padding: 101px 40px;
          display: flex;
          align-items: center;
          justify-content: center;

          .iot-description {
            max-width: 488px;
            font-family: Montserrat !important;
            font-weight: 500;
            font-style: Medium;
            font-size: 20px;
            letter-spacing: 0%;
            vertical-align: middle;
          }
        }
      }

      .iot-footer-text-container {
        display: flex;
        justify-content: center;
        .iot-footer-text {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          letter-spacing: 0%;
          text-align: justify;
          vertical-align: middle;
          max-width: 1240px;
        }
      }
    }

    /* =========  CONNECTIVITY SECTION  ========= */
    .connectivity-section {
      // padding: 5rem 8vw;
      padding: 83px 167px 145px 99px;
      background: #fff;

      .connectivity-top {
        display: flex;
        flex-wrap: wrap;
        gap: 259px;
        margin-bottom: 107px;

        .connectivity-headline {
          flex: 1 1 300px;
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          color: #d77d46;
        }

        .connectivity-paragraph {
          flex: 1 1 391px;
          max-width: 391px;
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          letter-spacing: 0%;
          color: #021F2E;
        }
      }

      /* ----------  Card row ---------- */
      .connectivity-cards {
        display: flex;
        // flex-wrap: wrap;
        gap: 42px;
        justify-content: center;

        
      }
    }
  }
}
