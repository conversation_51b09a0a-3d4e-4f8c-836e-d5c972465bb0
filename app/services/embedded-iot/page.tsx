"use state";
import { Box, Divider, Typography } from "@mui/material";
import Image from "next/image";
import "./EmbeddedIot.scss";
import {
  EmbeddedBanner,
  EmbeddedBannerlatest,
  EmbeddedBannersecond,
  EmbeddedPlatform,
  NetworkStack,
  SmartHomeBanner,
  SmartHomeBanner2,
  Versatilityicon,
  VersatilityImage,
} from "@/public";
import MechanicalTestingSwiper from "../mechanical-design/MechanicalTestingSwiper";
import ConnectivityCard from "@/components/ConnectivityCard/ConnectivityCard";

const EmbeddedIotPage = () => {
  const data = [
    {
      number: "01",
      title: "Gateway Design & Development",
      description:
        "We specialize in designing and developing multi-protocol supported edge computing devices that enable seamless communication between your end devices and cloud platforms and bridge the gap between diverse devices and networks",
    },
    {
      number: "02",
      title: "Network Connectivity",
      description:
        "We identify and deploy the most 	suitable communication technologies based on coverage, range, 	scalability, cost, and deployment requirements, ensuring security 	across the entire network.",
    },
    {
      number: "03",
      title: "Network Stack Integration",
      description:
        " We understand that implementing network stack is crucial to design and 	develop a robust IoT solution. Our team is experienced in various 	connectivity stack development like OCPP, DLMS, OPC UA",
    },
    {
      number: "04",
      title: "Standards/Open Source Support",
      description:
        " We ensure compliance with industry standards and integrate open-source solutions to provide flexible and cost-effective connectivity options.",
    },
    {
      number: "05",
      title: "Certification Compliance Support ",
      description:
        "Certification Compliance is necessary part of any product manufacturers , we help them to ensure that their product meet national and international standards for safety, accuracy, and interoperability.",
    },
    {
      number: "06",
      title: "Device & Network Management",
      description:
        "Develops algorithms and APIs which can easily manage your connected devices and networks starts from setup  configuration to troubleshooting and performance tuning and ensuring smooth operations.",
    },
  ];
  return (
    <Box className="embedded-iot-page-container">
      <Box className="embedded-iot-page-content">
        <Box className="embedded-iot-page-header">
          <Box className="ellipse-container">
            <Image
              src={EmbeddedBannerlatest}
              alt="Ellipse"
              className="ellipse-image"
            />
          </Box>

          <Box className="hero-content">
            
          </Box>
        </Box>
        {/* =========  NEXT SECTION: Embedded Solutions  ========= */}
        <Box className="embedded-solution-section">
          {/* Left – headline */}
          <Box className="solution-left">
            <Box className="solution-left-container">
              <Typography component="h2" className="solution-title">
                End-to-End Embedded System Solutions for Smarter, Low-Power
                Devices
              </Typography>
            </Box>
          </Box>

          {/* Right – description */}
          <Box className="solution-right">
            <Box className="solution-text-first-contaqiner">
              <Typography className="solution-text-first">
                Aadvik TekLabs is a specialized design services partner offering
                complete electronic system development—from hardware design to
                network protocols and certification. We build secure, connected,
                and intelligent embedded systems optimized for low‑power
                consumption and efficient performance.
              </Typography>
              <Typography className="solution-text-second">
                Serving industries such as smart homes, consumer electronics,
                HVAC, industrial machinery, and wearables, our expertise spans
                the entire product lifecycle—from PoC conceptualization to final
                compliance and certification—ensuring robust, future‑ready
                solutions tailored to your needs.
              </Typography>
            </Box>
          </Box>
        </Box>
        {/* =========  EMBEDDED PLATFORMS SECTION  ========= */}
        <Box className="embedded-platforms-section">
          {/* Heading */}
          <Typography component="h2" className="platforms-title">
            Embedded Platforms We Have Worked On
          </Typography>

          {/* Subtitle */}
          <Box className="platform-subtitle-container">
            <Typography className="platforms-subtitle">
              We craft tailored solutions across platforms, from low-power
              microcontrollers to high-performance processors. Combining
              hardware and software expertise, we deliver efficient, scalable,
              and seamlessly integrated systems.
            </Typography>
          </Box>

          {/* Single image with all logos */}
          <Box className="logos-wrapper">
            <Image
              src={EmbeddedPlatform}
              alt="Embedded platform partner logos"
              className="logo-strip"
              priority
            />
          </Box>
        </Box>
        {/* The versatility of our offerings are - */}
        <Box className="offerings-section-main-container">
          <Typography className="offerings-title">
            The versatility of our offerings are -
          </Typography>
          <Divider className="offerings-divider" />
          <Box className="offerings-section">
            {/* Left content */}
            <Box className="offerings-left">
              <Box className="offerings-list">
                {[
                  "Embedded System Design",
                  "Embedded UI/UX Design",
                  "Connectivity and Networking",
                  "Regulatory & Certification",
                  "Supply & Vendor Management",
                  "PoC, Prototyping & Manufacturing Support",
                ].map((text, idx) => (
                  <Box key={idx} className="offering-item">
                    <Image
                      src={Versatilityicon}
                      alt="offering icon"
                      className="offering-icon"
                    />
                    <Typography className="offering-text">{text}</Typography>
                  </Box>
                ))}
              </Box>
            </Box>

            {/* Right image */}
            <Box className="offerings-right">
              <Image
                src={VersatilityImage}
                alt="Offering Visual"
                className="offering-main-image"
                priority
              />
            </Box>
          </Box>
        </Box>

        {/* Network Stack Section */}
        <Box className="iot-network-section">
          {/* Section Title */}
          <Typography component="h2" className="iot-title">
            IoT Communication & Network Stack
          </Typography>

          {/* Main layout: image left, orange box right */}
          <Box className="iot-main-content">
            {/* Left-side image */}
            <Box className="iot-left-image">
              <Image
                src={NetworkStack}
                alt="IoT Illustration"
                className="iot-img"
                priority
              />
            </Box>

            {/* Right-side orange text box */}
            <Box className="iot-right-box">
              <Typography className="iot-description">
                Aadvik Teklabs recognizes that reliable connectivity and
                security are two crucial components for successful IoT
                deployment. Selecting the right hardware is always a key
                challenge for smooth and efficient roll out. Our team has
                end-to-end expertise in connectivity and security
                implementation, enabling seamless integration of any end device
                with edge gateway to the cloud—ensuring your IoT ecosystem is
                secure, scalable, and future-ready.
              </Typography>
            </Box>
          </Box>

          {/* Footer */}
          <Box className="iot-footer-text-container">
            <Typography className="iot-footer-text">
              IoT-based wireless communication enables seamless connectivity
              between smart devices, improving automation, efficiency, and
              data-driven decision-making across industries. The choice of
              wireless technology depends on factors like range, power
              consumption, and data rate requirements. Our Connectivity Services
              Includes –
            </Typography>
          </Box>
        </Box>

        {/* Swiper component */}
        <Box className="swiper-section-embedded-iot">
          <MechanicalTestingSwiper data={data} />
        </Box>
        {/* Connectivitry Section */}
        <Box className="connectivity-section">
          <Box className="connectivity-top">
            <Typography component="h2" className="connectivity-headline">
              Powering Sustainable <br/>Innovation with Smart,<br/> Secure Embedded
              <br/>Systems
            </Typography>

            <Typography className="connectivity-paragraph">
              In the growing era of green engineering, Aadvik specializes in
              developing secure, sustainable, and efficient electronic systems.
              From adding connectivity to legacy devices to complete system
              design, we create reliable technology building blocks that meet
              your needs while ensuring environmental sustainability and high
              performance.
            </Typography>
          </Box>

          {/* Bottom row: three cards */}
          <Box className="connectivity-cards">
            <ConnectivityCard
              title="LAN, WAN & M2M"
              technologies="WiFi, LoRa, 6LoWPAN, 3G/4G/LTE, Ethernet"
              backgroundColor="#7BD3A3"
            />

            <ConnectivityCard
              title="Cloud Connectivity"
              technologies="REST API, MQTT, CoAP, HTTP, WebSocket"
              backgroundColor="#FBDB7B"
            />

            <ConnectivityCard
              title="Device Connectivity"
              technologies="BLE Mesh, Bluetooth, ZigBee, Z‑Wave, DALI, DMX, Modbus"
              backgroundColor="#97CBEB"
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default EmbeddedIotPage;
