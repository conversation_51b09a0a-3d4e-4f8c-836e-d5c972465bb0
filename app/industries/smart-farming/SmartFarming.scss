.smart-farming-page-container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;

  .smart-farming-page-content {
    width: 100vw;
    min-height: 100vh;
    position: relative;

    .smart-farming-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
      position: relative;

      .ellipse-container {
        width: 100vw;
        height: 100vh;
        position: relative;
        z-index: 1;

        .banner-gradient {
          position: absolute;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          pointer-events: none;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.5) 100%
          );
          z-index: 2;
        }

        img {
          width: 100vw;
          height: 100vh;
          object-fit: cover;
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }

      .hero-content {
        position: absolute;
        top: 35%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 999px;
        height: 124px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;
        color: #fff;
        text-align: center;
        padding-bottom: 0;

        .title {
          font-family: Inter !important;
          font-weight: 800;
          font-style: Extra Bold;
          font-size: 64px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          color: #eaecee;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    .smart-farming-info-section {
      display: flex;
      justify-content: flex-end;
      align-items: flex-start;
      padding: 80px 106px 137px 92px;
      gap: 200px;
      background-color: #ffffff;

      .left-content {
        flex: 1;
        max-width: 599px;

        .info-title {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          color: #d77d46;
        }
      }

      .right-content {
        flex: 1;

        .info-description {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          letter-spacing: 0%;
          max-width: 483px;
        }
        .info-description-two {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 16px;
          letter-spacing: 0%;
          max-width: 483px;
          margin-top: 20px;
        }
      }

      @media (max-width: 960px) {
        flex-direction: column;
        padding: 40px 20px;
        gap: 30px;

        .left-content,
        .right-content {
          width: 100%;
        }

        .info-title {
          font-size: 24px;
          text-align: left;
        }

        .info-description {
          font-size: 14px;
        }
      }
    }
    .smart-farming-support-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 213px 75px 94px;
      gap: 40px;
      background-color: #fff;

      .support-left {
        flex: 1;
        max-width: 50%;

        .support-title {
          font-family: Inter !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          color: #021f2e;

          margin-bottom: 45px;
        }

        .support-list {
          padding: 0;

          .support-item {
            align-items: flex-start;

            .MuiListItemIcon-root {
              min-width: 30px;
            }

            .MuiListItemText-root {
              font-family: Montserrat !important;
              font-weight: 700 !important;
              font-style: Bold !important;
              font-size: 20px !important;
              color: #021f2e !important;
              line-height: 24px !important;
              letter-spacing: 0%;
              margin-top: auto;
              margin-bottom: auto;
            }
          }
        }
      }

      .support-right {
        flex: 1;
        max-width: 50%;
        display: flex;
        justify-content: center;

        .support-image {
          max-width: 100%;
          height: auto;
        }
      }

      @media (max-width: 768px) {
        flex-direction: column;
        padding: 40px 20px;

        .support-left,
        .support-right {
          max-width: 100%;
        }

        .support-title {
          font-size: 24px;
        }
      }
    }
    .smart-farming-feature-section {
      position: relative;
      align-items: center;
      padding: 97px 25px 0 120px;

      .feature-left {
        z-index: 2;

        .feature-title {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          color: #fff;

          span {
            color: #d77d46;
          }
        }
      }

      /* ─── right column ─── */
      .feature-right {
        display: flex;
        justify-content: flex-end;
        gap: 24px;
        z-index: 2;
        padding-right: 300px;

        .feature-icon {
          color: #d77d46;
          font-size: 46px !important; // override MUI default
        }

        .feature-heading {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 52px;
          letter-spacing: 0%;
          color: #fff;
        }
      }

      /* ─── responsive tweaks ─── */
      @media (max-width: 960px) {
        flex-direction: column;
        padding: 60px 24px;
        text-align: center;

        .feature-left,
        .feature-right {
          flex: 1 1 100%;
        }

        .feature-right {
          justify-content: center;
          margin-top: 32px;
        }
      }
    }
    .swiper-section-smart-farming {
      margin: 67px 69px 67px 96px;
    }
    .smart-farming-iot-section {
      padding: 100px 80px;
      background-color: #ffffff;
      text-align: center;
      .iot-top-heading {
        display: flex;
        justify-content: center;
        .iot-main-heading {
          font-family: Inter !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          align-items: center;
          max-width: 1048px;

          margin-bottom: 43px;
        }
      }

      .iot-content {
        display: flex;
        // justify-content: space-between;
        align-items: flex-start;
        // gap: 82px;
        text-align: left;

        .iot-left-text {
          flex: 1;
          display: flex;
          flex-direction: column;
          margin-top: 130px;
          gap: 102px;

          .iot-paragraph {
            font-family: Montserrat !important;
            font-weight: 500;
            font-style: Medium;
            font-size: 16px;
            line-height: 24px;
            letter-spacing: 0%;
            max-width: 525px;
            color: #021f2e;
          }
        }

        .iot-image-container {
          flex: 1;
          display: flex;
          justify-content: center;

          .iot-image {
            width: 100%;
            max-width: 752px;
            // height: auto;
          }
        }
      }

      .iot-bottom-highlight-container {
        display: flex;
        justify-content: center;
        .iot-bottom-highlight {
          margin-top: 60px;
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 40px;
          letter-spacing: 0%;
          text-align: center;
          color: #d77d46;
          max-width: 898px;
        }
      }

      @media (max-width: 960px) {
        padding: 60px 24px;

        .iot-content {
          flex-direction: column;

          .iot-image-container {
            margin-top: 32px;
          }
        }

        .iot-main-heading {
          font-size: 24px;
        }

        .iot-bottom-highlight {
          font-size: 20px;
        }
      }
    }
  }
}
