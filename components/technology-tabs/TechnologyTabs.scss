.technology-tabs-container {
    .MuiTabs-indicator {
        display: none;
    }

    .MuiTabs-root {
        .MuiTabs-list {
            gap: 20px;

            .MuiButtonBase-root {
                font-family: Montserrat !important;
                font-weight: 500;
                font-style: Medium;
                font-size: 20px;
                leading-trim: CAP_HEIGHT;
                line-height: 100%;
                letter-spacing: 0%;
                vertical-align: middle;
                text-transform: none;
                background: #FFFFFF;
                color: #000000;
                border-radius: 8px;
                min-width: 205px;

            }

            .Mui-selected {
                font-family: Montserrat !important;
                font-weight: 800;
                font-style: ExtraBold;
                font-size: 20px;
                leading-trim: CAP_HEIGHT;
                line-height: 100%;
                letter-spacing: 0%;
                vertical-align: middle;
                color: #FFFFFF;
                background: #D77D46;


            }
        }
    }
}

.tech-tab-img {
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
}

.tech-tab-img.fade-in {
    opacity: 1;
}

.tech-tab-img.fade-out {
    opacity: 0;
}